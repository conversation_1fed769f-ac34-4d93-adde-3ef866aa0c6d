import { Form, Input, Select } from 'antd'
import type { ReactNode } from 'react'
import { Eye, EyeOff } from 'lucide-react'

interface FormFieldProps {
  label: string
  name: string
  required?: boolean
  type?: 'text' | 'email' | 'password' | 'select'
  placeholder?: string
  rules?: any[]
  dependencies?: string[]
  children?: ReactNode
  options?: { value: string; label: string }[]
}

export function FormField({
  label,
  name,
  required = false,
  type = 'text',
  placeholder,
  rules = [],
  dependencies,
  children,
  options
}: FormFieldProps) {
  const defaultRules = []
  
  if (required) {
    defaultRules.push({ required: true, message: `Please input your ${label.toLowerCase()}!` })
  }
  
  if (type === 'email') {
    defaultRules.push({ type: 'email', message: 'Please enter a valid email!' })
  }

  const allRules = [...defaultRules, ...rules]

  const inputClasses = "h-12 rounded-lg border-border-light hover:border-border-medium focus:border-brand-primary"

  const renderInput = () => {
    switch (type) {
      case 'password':
        return (
          <Input.Password
            placeholder={placeholder}
            className={inputClasses}
            iconRender={(visible) => (visible ? <Eye size={16} /> : <EyeOff size={16} />)}
          />
        )
      case 'select':
        return (
          <Select
            placeholder={placeholder}
            className="h-12"
            size="large"
          >
            {options?.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        )
      default:
        return (
          <Input
            placeholder={placeholder}
            className={inputClasses}
          />
        )
    }
  }

  return (
    <Form.Item
      label={
        <span className="text-text-secondary font-medium">
          {label} {required && <span className="text-error">*</span>}
        </span>
      }
      name={name}
      rules={allRules}
      dependencies={dependencies}
    >
      {children || renderInput()}
    </Form.Item>
  )
}