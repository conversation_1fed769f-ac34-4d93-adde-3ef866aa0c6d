export const MAX_FILE_SIZE = 5 * 1024 * 1024 * 1024 // 5GB
export const ALLOWED_EXTENSIONS = ['.edf']

export interface FileValidationResult {
  isValid: boolean
  errorMessage?: string
}

export function validateFileSize(file: File): FileValidationResult {
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      errorMessage: `File size exceeds ${MAX_FILE_SIZE / (1024 * 1024)}MB limit`,
    }
  }
  return { isValid: true }
}

export function validateFileExtension(file: File): FileValidationResult {
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
  if (!ALLOWED_EXTENSIONS.includes(fileExtension)) {
    return {
      isValid: false,
      errorMessage: `Only ${ALLOWED_EXTENSIONS.join(', ')} files are allowed`,
    }
  }
  return { isValid: true }
}

export function validateFile(file: File): FileValidationResult {
  const sizeValidation = validateFileSize(file)
  if (!sizeValidation.isValid) return sizeValidation

  const extensionValidation = validateFileExtension(file)
  if (!extensionValidation.isValid) return extensionValidation

  return { isValid: true }
}

export function validateFiles(files: File[]): File[] {
  return files.filter((file) => validateFile(file).isValid)
}

export function getInvalidFiles(files: File[]): Array<{ file: File; error: string }> {
  return files
    .map((file) => {
      const validation = validateFile(file)
      return validation.isValid ? null : { file, error: validation.errorMessage || 'Invalid file' }
    })
    .filter(Boolean) as Array<{ file: File; error: string }>
}

export interface DuplicateCheckResult {
  duplicates: File[]
  unique: File[]
}

export function checkDuplicateFiles(
  newFiles: File[],
  existingFiles: Array<{ name: string; size: number }>
): DuplicateCheckResult {
  const duplicates = newFiles.filter((newFile) =>
    existingFiles.some(
      (existing) => existing.name === newFile.name && existing.size === newFile.size
    )
  )
  const unique = newFiles.filter((newFile) => !duplicates.includes(newFile))
  return { duplicates, unique }
}
