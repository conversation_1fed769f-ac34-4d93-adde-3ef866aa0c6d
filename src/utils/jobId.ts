/**
 * Generates a unique job ID using timestamp and random string
 * Format: {timestamp}_{randomString}
 * Example: 1703123456789_abc123def
 */
export function generateJobId(): string {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substr(2, 9)
  return `${timestamp}_${randomString}`
}

/**
 * Validates if a job ID follows the expected format
 */
export function isValidJobId(jobId: string): boolean {
  const pattern = /^\d+_[a-z0-9]+$/
  return pattern.test(jobId)
}

/**
 * Extracts timestamp from job ID
 */
export function getTimestampFromJobId(jobId: string): number | null {
  if (!isValidJobId(jobId)) {
    return null
  }
  
  const timestamp = parseInt(jobId.split('_')[0], 10)
  return isNaN(timestamp) ? null : timestamp
}
