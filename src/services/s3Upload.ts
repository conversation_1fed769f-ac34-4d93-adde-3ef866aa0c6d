import { apiService } from './api'
import { generateJobId } from '../utils/jobId'
import { handleApiError, showToast } from '../utils/toast'

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void
  onSuccess?: (key: string) => void
  onError?: (error: Error) => void
  signal?: AbortSignal
}

export interface UploadResult {
  success: boolean
  key?: string
  jobId?: string
  error?: string
}

export class S3UploadService {
  private activeUploads = new Map<string, XMLHttpRequest>()

  async uploadFile(file: File, options: UploadOptions = {}): Promise<UploadResult> {
    const { onProgress, onSuccess, onError, signal } = options

    try {
      // Generate unique job ID
      const jobId = generateJobId()
      const key = `input/${jobId}.edf`

      // Get presigned URL
      const presignedData = await apiService.getPresignedUrl({
        bucket: 'biormika-edf-input',
        key,
      })

      // Create XMLHttpRequest for upload with progress tracking
      const xhr = new XMLHttpRequest()
      const uploadId = `upload_${jobId}`

      // Store the request for potential cancellation
      this.activeUploads.set(uploadId, xhr)

      return new Promise<UploadResult>((resolve, reject) => {
        // Handle abort signal
        if (signal) {
          signal.addEventListener('abort', () => {
            xhr.abort()
            this.activeUploads.delete(uploadId)
            resolve({
              success: false,
              error: 'Upload cancelled by user',
            })
          })
        }

        // Upload progress tracking
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const progress: UploadProgress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100),
            }
            onProgress(progress)
          }
        })

        // Upload completion
        xhr.addEventListener('load', () => {
          this.activeUploads.delete(uploadId)

          if (xhr.status >= 200 && xhr.status < 300) {
            const result: UploadResult = {
              success: true,
              key,
              jobId,
            }

            if (onSuccess) {
              onSuccess(key)
            }

            showToast.success('File uploaded successfully!')
            resolve(result)
          } else {
            const error = `Upload failed with status: ${xhr.status}`
            const result: UploadResult = {
              success: false,
              error,
            }

            if (onError) {
              onError(new Error(error))
            }

            handleApiError({ message: error })
            resolve(result)
          }
        })

        // Upload error handling
        xhr.addEventListener('error', () => {
          this.activeUploads.delete(uploadId)
          const error = 'Network error during upload'
          const result: UploadResult = {
            success: false,
            error,
          }

          if (onError) {
            onError(new Error(error))
          }

          handleApiError({ message: error })
          resolve(result)
        })

        // Upload abort handling
        xhr.addEventListener('abort', () => {
          this.activeUploads.delete(uploadId)
          const result: UploadResult = {
            success: false,
            error: 'Upload cancelled',
          }
          resolve(result)
        })

        // Start the upload
        xhr.open('PUT', presignedData.uploadUrl)
        xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream')
        xhr.send(file)
      })
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to initiate upload'

      if (onError) {
        onError(new Error(errorMessage))
      }

      return {
        success: false,
        error: errorMessage,
      }
    }
  }

  cancelUpload(uploadId: string): boolean {
    const xhr = this.activeUploads.get(uploadId)
    if (xhr) {
      xhr.abort()
      this.activeUploads.delete(uploadId)
      return true
    }
    return false
  }

  cancelAllUploads(): void {
    this.activeUploads.forEach((xhr, uploadId) => {
      xhr.abort()
      this.activeUploads.delete(uploadId)
    })
  }

  getActiveUploadCount(): number {
    return this.activeUploads.size
  }
}

export const s3UploadService = new S3UploadService()
