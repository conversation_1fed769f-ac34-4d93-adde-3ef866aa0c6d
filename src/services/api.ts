import axios from 'axios'
import type { AxiosResponse } from 'axios'
import { handleApiError } from '../utils/toast'

const API_BASE_URL = 'https://gtd4gh13p7.execute-api.us-east-1.amazonaws.com/dev'

export interface PresignedUrlRequest {
  bucket: string
  key: string
}

export interface PresignedUrlResponse {
  uploadUrl: string
  key: string
  bucket: string
}

export interface ApiError {
  message: string
  statusCode: number
  details?: any
}

class ApiService {
  private async makeRequest<T>(
    requestFn: () => Promise<AxiosResponse<T>>,
    retries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await requestFn()
        return response.data
      } catch (error: any) {
        const isLastAttempt = attempt === retries
        const isRetryableError = this.isRetryableError(error)

        if (isLastAttempt || !isRetryableError) {
          throw this.formatError(error)
        }

        // Wait before retrying with exponential backoff
        await this.delay(delay * Math.pow(2, attempt - 1))
      }
    }

    throw new Error('Max retries exceeded')
  }

  private isRetryableError(error: any): boolean {
    if (!error.response) {
      // Network errors are retryable
      return true
    }

    const status = error.response.status
    // Retry on server errors (5xx) and rate limiting (429)
    return status >= 500 || status === 429
  }

  private formatError(error: any): ApiError {
    if (error.response) {
      return {
        message: error.response.data?.message || error.message || 'API request failed',
        statusCode: error.response.status,
        details: error.response.data,
      }
    }

    if (error.request) {
      return {
        message: 'Network error - please check your connection',
        statusCode: 0,
      }
    }

    return {
      message: error.message || 'Unknown error occurred',
      statusCode: 0,
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  async getPresignedUrl(request: PresignedUrlRequest): Promise<PresignedUrlResponse> {
    try {
      return await this.makeRequest(() =>
        axios.post<PresignedUrlResponse>(`${API_BASE_URL}/get-upload-url`, request, {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10 second timeout
        })
      )
    } catch (error) {
      handleApiError(error)
      throw error
    }
  }
}

export const apiService = new ApiService()
